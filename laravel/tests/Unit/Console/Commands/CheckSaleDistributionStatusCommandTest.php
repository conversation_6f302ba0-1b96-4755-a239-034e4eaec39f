<?php

namespace Tests\Unit\Console\Commands;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Sales\Ceiling\DistributionService;
use Mockery;
use Tests\TestCase;

class CheckSaleDistributionStatusCommandTest extends TestCase
{
    private $distributionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->distributionService = Mockery::mock(DistributionService::class);
        $this->app->instance(DistributionService::class, $this->distributionService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_shows_error_for_invalid_sale_id()
    {
        $this->artisan('distribution:check-sale-status', ['sale_id' => 'abc'])
            ->expectsOutput('Sale ID must be a positive integer')
            ->assertExitCode(1);
    }

    /** @test */
    public function it_shows_error_for_non_existent_sale()
    {
        // Mock Sale::find to return null (sale not found)
        $this->partialMock(Sale::class, function ($mock) {
            $mock->shouldReceive('with')
                ->with(['product', 'distributor', 'mappings', 'details'])
                ->andReturnSelf();
            $mock->shouldReceive('find')
                ->with(99999)
                ->andReturn(null);
        });

        $this->artisan('distribution:check-sale-status', ['sale_id' => 99999])
            ->expectsOutput('Sale with ID 99999 not found')
            ->assertExitCode(1);
    }

    /** @test */
    public function it_correctly_identifies_below_ceiling_sale()
    {
        // Mock Sale::find to return a BELOW ceiling sale
        $sale = $this->createMockSale([
            'id' => 123,
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 50,
            'value' => 1000,
            'bonus' => 10
        ]);

        $this->partialMock(Sale::class, function ($mock) use ($sale) {
            $mock->shouldReceive('with')
                ->with(['product', 'distributor', 'mappings', 'details'])
                ->andReturnSelf();
            $mock->shouldReceive('find')
                ->with(123)
                ->andReturn($sale);
        });

        // Mock the distribution service to return empty results
        $this->distributionService
            ->shouldReceive('queryCeilingSales')
            ->andReturn(collect([]));

        $this->artisan('distribution:check-sale-status', ['sale_id' => 123])
            ->expectsOutput('Distributed: ❌ NO')
            ->expectsOutput('Ceiling Status: BELOW (0)')
            ->expectsOutput('📋 This sale HAS NOT BEEN DISTRIBUTED')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_correctly_identifies_distributed_sale()
    {
        // Mock Sale::find to return a DISTRIBUTED ceiling sale
        $sale = $this->createMockSale([
            'id' => 456,
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'quantity' => 150,
            'value' => 3000,
            'bonus' => 25,
            'sale_ids' => '1,2,3'
        ]);

        $this->partialMock(Sale::class, function ($mock) use ($sale) {
            $mock->shouldReceive('with')
                ->with(['product', 'distributor', 'mappings', 'details'])
                ->andReturnSelf();
            $mock->shouldReceive('find')
                ->with(456)
                ->andReturn($sale);
        });

        $this->artisan('distribution:check-sale-status', ['sale_id' => 456])
            ->expectsOutput('Distributed: ✅ YES')
            ->expectsOutput('Ceiling Status: DISTRIBUTED (2)')
            ->expectsOutput('🎯 This sale HAS BEEN DISTRIBUTED')
            ->expectsOutput('→ This is a distributed sale created from ceiling violation')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_correctly_identifies_above_ceiling_sale()
    {
        // Mock Sale::find to return an ABOVE ceiling sale
        $sale = $this->createMockSale([
            'id' => 789,
            'ceiling' => Ceiling::ABOVE->value,
            'quantity' => 200,
            'value' => 4000,
            'bonus' => 30,
            'product_id' => 1,
            'distributor_id' => 1,
            'date' => '2024-01-15'
        ]);

        // Mock Sale::where to return related distributed sales
        $relatedSale = $this->createMockSale([
            'id' => 999,
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'product_id' => 1,
            'distributor_id' => 1,
            'date' => '2024-01-15',
            'sale_ids' => '789,999'
        ]);

        $this->partialMock(Sale::class, function ($mock) use ($sale, $relatedSale) {
            $mock->shouldReceive('with')
                ->with(['product', 'distributor', 'mappings', 'details'])
                ->andReturnSelf();
            $mock->shouldReceive('find')
                ->with(789)
                ->andReturn($sale);
            $mock->shouldReceive('where')
                ->with('ceiling', Ceiling::DISTRIBUTED->value)
                ->andReturnSelf();
            $mock->shouldReceive('where')
                ->with('product_id', 1)
                ->andReturnSelf();
            $mock->shouldReceive('where')
                ->with('distributor_id', 1)
                ->andReturnSelf();
            $mock->shouldReceive('where')
                ->with('date', '2024-01-15')
                ->andReturnSelf();
            $mock->shouldReceive('whereNotNull')
                ->with('sale_ids')
                ->andReturnSelf();
            $mock->shouldReceive('get')
                ->andReturn(collect([$relatedSale]));
        });

        $this->artisan('distribution:check-sale-status', ['sale_id' => 789])
            ->expectsOutput('Distributed: ✅ YES')
            ->expectsOutput('Ceiling Status: ABOVE (1)')
            ->expectsOutput('🎯 This sale HAS BEEN DISTRIBUTED')
            ->expectsOutput('→ This is an original sale that was distributed (marked as ABOVE)')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_shows_detailed_analysis_when_requested()
    {
        $sale = $this->createMockSale([
            'id' => 111,
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 50,
            'value' => 1000,
            'bonus' => 10
        ]);

        $this->partialMock(Sale::class, function ($mock) use ($sale) {
            $mock->shouldReceive('with')
                ->with(['product', 'distributor', 'mappings', 'details'])
                ->andReturnSelf();
            $mock->shouldReceive('find')
                ->with(111)
                ->andReturn($sale);
        });

        // Mock the distribution service
        $this->distributionService
            ->shouldReceive('queryCeilingSales')
            ->andReturn(collect([]));

        $this->artisan('distribution:check-sale-status', [
                'sale_id' => 111,
                '--detailed' => true
            ])
            ->expectsOutput('=== DETAILED ANALYSIS ===')
            ->expectsOutput('--- Mapping Details ---')
            ->expectsOutput('--- Sales Details Breakdown ---')
            ->expectsOutput('--- Distribution Service Query Simulation ---')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_validates_distribution_eligibility_criteria()
    {
        $sale = $this->createMockSale([
            'id' => 222,
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 50,
            'value' => 1000,
            'bonus' => 10
        ]);

        $this->partialMock(Sale::class, function ($mock) use ($sale) {
            $mock->shouldReceive('with')
                ->with(['product', 'distributor', 'mappings', 'details'])
                ->andReturnSelf();
            $mock->shouldReceive('find')
                ->with(222)
                ->andReturn($sale);
        });

        $this->artisan('distribution:check-sale-status', ['sale_id' => 222])
            ->expectsOutput('=== DISTRIBUTION ELIGIBILITY ANALYSIS ===')
            ->expectsOutput('Criteria Checks:')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_handles_command_execution_errors_gracefully()
    {
        // Mock a sale that might cause issues
        $sale = $this->createMockSale([
            'id' => 333,
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 50,
            'value' => 1000,
            'bonus' => 10
        ]);

        $this->partialMock(Sale::class, function ($mock) use ($sale) {
            $mock->shouldReceive('with')
                ->with(['product', 'distributor', 'mappings', 'details'])
                ->andReturnSelf();
            $mock->shouldReceive('find')
                ->with(333)
                ->andReturn($sale);
        });

        // Mock the distribution service to throw an exception
        $this->distributionService
            ->shouldReceive('queryCeilingSales')
            ->andThrow(new \Exception('Database connection error'));

        $this->artisan('distribution:check-sale-status', [
                'sale_id' => 333,
                '--detailed' => true
            ])
            ->expectsOutput('Error simulating distribution query: Database connection error')
            ->assertExitCode(0);
    }

    /**
     * Create a mock Sale object for testing
     */
    private function createMockSale(array $attributes = []): Sale
    {
        $sale = Mockery::mock(Sale::class);
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) use ($attributes) {
            return $attributes[$key] ?? null;
        });

        // Allow common Eloquent methods that might be called
        $sale->shouldReceive('load')->andReturnSelf();
        $sale->shouldReceive('mappings')->andReturnSelf();
        $sale->shouldReceive('details')->andReturn(collect([]));

        // Set properties directly for easy access
        foreach ($attributes as $key => $value) {
            $sale->$key = $value;
        }

        return $sale;
    }
}
